# -*- coding: utf-8 -*-
"""
Discharge Summary 内涵质控子控制器
负责加载和管理该文档类型下所有内涵质控规则的prompt配置
"""
import json
import sys
from pathlib import Path

# 添加父目录到路径以导入模块
sys.path.append(str(Path(__file__).parent.parent.parent))
from model_use import llm_use
from config import qwen_32B_config

class DischargeSummaryConnotationController:
    """
    Discharge Summary 内涵质控控制器
    """

    def __init__(self):
        self.document_type = "Discharge Summary"
        self.document_type_dir = "Discharge_Summary"
        self.base_dir = Path(__file__).parent
        self.prompts_config_file = self.base_dir / "discharge_summary_prompts.json"
        self.prompts_config = self._load_prompts_config()
        self.total_rules = len(self.prompts_config.get("prompts", {}))

    def _load_prompts_config(self):
        """
        加载prompt配置文件

        Returns:
            dict: prompt配置数据
        """
        try:
            with open(self.prompts_config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"警告: 未找到prompt配置文件 {self.prompts_config_file}")
            return {"document_type": self.document_type, "prompts": {}}
        except json.JSONDecodeError as e:
            print(f"错误: 解析prompt配置文件失败: {e}")
            return {"document_type": self.document_type, "prompts": {}}

    def run_connotation_quality_control(self, medical_record):
        """
        运行所有内涵质控检查

        Args:
            medical_record (dict): 病历数据

        Returns:
            dict: 内涵质控结果
        """
        results = {}

        print(f"开始执行 {self.document_type} 内涵质控，共 {self.total_rules} 条规则...")

        for rule_id, prompt_config in self.prompts_config.get("prompts", {}).items():
            try:
                # 执行单个内涵质控规则
                result = self._execute_single_rule(rule_id, prompt_config, medical_record)

                rule_info = prompt_config.get("rule_info", {})
                rule_content = rule_info.get("rule_content", rule_id)

                results[rule_content] = {
                    'rule_id': rule_id,
                    'rule_type': rule_info.get('rule_type_chinese', '未知'),
                    'classification': rule_info.get('classification_chinese', '未知'),
                    'deduction_points': rule_info.get('deduction_points', 0),
                    'score': result.get('score', 0),
                    'problems': result.get('problems', ''),
                    'suggestions': result.get('suggestions', ''),
                    'type': 'connotation'
                }

                print(f"  ✓ 完成规则 {rule_id}")

            except Exception as e:
                print(f"  ✗ 规则 {rule_id} 执行失败: {e}")
                rule_info = prompt_config.get("rule_info", {})
                rule_content = rule_info.get("rule_content", rule_id)

                results[rule_content] = {
                    'rule_id': rule_id,
                    'rule_type': rule_info.get('rule_type_chinese', '未知'),
                    'classification': rule_info.get('classification_chinese', '未知'),
                    'deduction_points': rule_info.get('deduction_points', 0),
                    'score': 0,
                    'problems': '执行失败',
                    'suggestions': '请检查规则配置',
                    'type': 'connotation',
                    'error': str(e)
                }

        print(f"{self.document_type} 内涵质控执行完成，共检查 {len(results)} 条规则")
        return results

    def _execute_single_rule(self, rule_id, prompt_config, medical_record):
        """
        执行单个内涵质控规则

        Args:
            rule_id (str): 规则ID
            prompt_config (dict): prompt配置
            medical_record (dict): 病历数据

        Returns:
            dict: 质控结果
        """
        system_prompt = prompt_config.get("system_prompt", "")
        user_prompt_template = prompt_config.get("user_prompt", "")

        # 格式化用户提示词
        user_prompt = user_prompt_template.format(
            medical_record_content=medical_record.get("content", ""),
            patient_info=medical_record.get("patient_info", {})
        )

        # 调用大模型进行分析
        response = llm_use(system_prompt, user_prompt, qwen_32B_config)

        # 解析响应结果
        return self._parse_llm_response(response, prompt_config)

    def _parse_llm_response(self, response, prompt_config):
        """
        解析LLM响应结果

        Args:
            response (str): LLM响应
            prompt_config (dict): prompt配置

        Returns:
            dict: 解析后的结果
        """
        try:
            # 简单的文本解析逻辑
            # 这里可以根据实际需要实现更复杂的解析逻辑

            rule_info = prompt_config.get("rule_info", {})
            max_points = rule_info.get("deduction_points", 100)

            # 默认结果
            result = {
                "score": max_points,  # 默认满分
                "problems": "",
                "suggestions": ""
            }

            if response:
                # 尝试从响应中提取信息
                lines = response.strip().split('\n')
                for line in lines:
                    line = line.strip()
                    if '质控分数' in line or '得分' in line:
                        # 尝试提取分数
                        import re
                        score_match = re.search(r'(\d+)', line)
                        if score_match:
                            result["score"] = int(score_match.group(1))
                    elif '质控问题' in line or '问题' in line:
                        result["problems"] = line
                    elif '质控建议' in line or '建议' in line:
                        result["suggestions"] = line

            return result

        except Exception as e:
            print(f"解析LLM响应失败: {e}")
            rule_info = prompt_config.get("rule_info", {})
            return {
                "score": 0,
                "problems": "解析失败",
                "suggestions": "请检查LLM响应格式",
                "error": str(e)
            }

    def get_summary(self, results):
        """
        获取内涵质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 摘要信息
        """
        total_rules = len(results)
        total_score = sum(r.get('score', 0) for r in results.values())
        max_possible_score = sum(r.get('deduction_points', 0) for r in results.values())

        return {
            'document_type': self.document_type,
            'total_rules': total_rules,
            'total_score': total_score,
            'max_possible_score': max_possible_score,
            'score_rate': total_score / max_possible_score * 100 if max_possible_score > 0 else 0
        }

# 兼容性函数，供主控制器调用
def run_connotation_quality_control(medical_record):
    """兼容性函数"""
    controller = DischargeSummaryConnotationController()
    return controller.run_connotation_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = DischargeSummaryConnotationController()

    test_record = {
        "content": "测试Discharge Summary内容",
        "patient_info": {"name": "张三", "age": 45}
    }

    results = controller.run_connotation_quality_control(test_record)
    summary = controller.get_summary(results)

    print("\n内涵质控结果摘要:")
    print(f"文档类型: {summary['document_type']}")
    print(f"总规则数: {summary['total_rules']}")
    print(f"总得分: {summary['total_score']}")
    print(f"最高分: {summary['max_possible_score']}")
    print(f"得分率: {summary['score_rate']:.2f}%")
