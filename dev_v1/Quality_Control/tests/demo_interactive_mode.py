# -*- coding: utf-8 -*-
"""
交互式模式演示脚本
展示质控代码生成器的交互式功能
"""
import sys
from pathlib import Path

# 添加父目录路径以导入质控模块
sys.path.append(str(Path(__file__).parent.parent))

from quality_control_generator import QualityControlGenerator

def demo_document_selection():
    """演示文档类型选择功能"""
    print("=" * 80)
    print("📋 演示：文档类型交互式选择")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    # 获取可用文档
    documents = generator.get_available_documents()
    
    print(f"系统中共有 {len(documents)} 种文档类型可供选择：")
    for i, (doc_type, json_file) in enumerate(documents.items(), 1):
        chinese_name = generator._get_chinese_name(doc_type)
        if chinese_name:
            print(f"  {i}. {doc_type} ({chinese_name})")
        else:
            print(f"  {i}. {doc_type}")
    
    print("\n支持的输入方式：")
    print("  • 编号输入：1, 2, 3...")
    print("  • 完整名称：Discharge Summary")
    print("  • 简化名称：discharge summary, discharge_summary")
    print("  • 大小写不敏感：DISCHARGE SUMMARY")
    print("  • 特殊命令：list (重新显示), quit (退出)")

def demo_model_selection():
    """演示模型配置选择功能"""
    print("\n" + "=" * 80)
    print("🤖 演示：模型配置交互式选择")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    print("可用的模型配置：")
    for i, (key, model_info) in enumerate(generator.available_models.items(), 1):
        print(f"  {i}. {model_info['name']} ({key})")
        print(f"     {model_info['description']}")
    
    print("\n配置方案选项：")
    print("  1. 使用默认配置 (推荐)")
    print("     • 规则质控: GLM-4.5-Flash")
    print("     • 内涵质控: GLM-4.5-Flash")
    print()
    print("  2. 使用经典配置")
    print("     • 规则质控: GLM-4.5-Flash")
    print("     • 内涵质控: Qwen-32B")
    print()
    print("  3. 自定义配置")
    print("     • 分别选择规则质控和内涵质控的模型")
    print()
    print("  4. 全部使用GLM-4.5-Flash")
    print("     • 统一使用GLM-4.5-Flash模型")

def demo_input_validation():
    """演示输入验证功能"""
    print("\n" + "=" * 80)
    print("🔍 演示：输入验证和错误处理")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    print("文件名标准化示例：")
    test_inputs = [
        "discharge_summary.json",
        "discharge summary", 
        "Discharge Summary",
        "DISCHARGE SUMMARY",
        "initial progress note",
        "Initial Progress Note"
    ]
    
    for input_name in test_inputs:
        normalized = generator.normalize_filename(input_name)
        print(f"  '{input_name}' → '{normalized}'")
    
    print("\n文件查找验证：")
    documents = generator.get_available_documents()
    if documents:
        first_doc = list(documents.keys())[0]
        test_variations = [
            first_doc,
            first_doc.lower(),
            first_doc.replace(' ', '_').lower(),
            first_doc.lower().replace('_', ' ')
        ]
        
        for variation in test_variations:
            found = generator.find_json_file(variation)
            status = "✅ 找到" if found else "❌ 未找到"
            print(f"  '{variation}' → {status}")

def demo_chinese_support():
    """演示中文支持功能"""
    print("\n" + "=" * 80)
    print("🇨🇳 演示：中文界面支持")
    print("=" * 80)
    
    generator = QualityControlGenerator()
    
    print("中文名称映射：")
    mappings = [
        ("Discharge Summary", "出院记录"),
        ("Initial Progress Note", "首次病程记录"),
        ("Emergency Record", "急诊记录"),
        ("Surgery Record", "手术记录"),
        ("Consultation Record", "会诊记录"),
        ("Admission Record", "入院记录")
    ]
    
    for english, expected_chinese in mappings:
        chinese = generator._get_chinese_name(english)
        if chinese:
            print(f"  {english} → {chinese}")
        else:
            print(f"  {english} → (无中文映射)")
    
    print("\n用户界面特色：")
    print("  • 🎯 使用emoji图标增强可读性")
    print("  • 📋 清晰的菜单结构和选项说明")
    print("  • ✅ 友好的确认和错误提示")
    print("  • 🔧 详细的配置摘要显示")

def demo_compatibility():
    """演示兼容性保证"""
    print("\n" + "=" * 80)
    print("🔄 演示：向后兼容性保证")
    print("=" * 80)
    
    print("现有命令行功能完全保留：")
    print()
    print("1. 列出可用文件：")
    print("   python quality_control_generator.py --list")
    print("   python quality_control_generator.py -l")
    print()
    print("2. 处理特定文件：")
    print("   python quality_control_generator.py --file discharge_summary")
    print("   python quality_control_generator.py -f 'Discharge Summary'")
    print()
    print("3. 新增交互式模式：")
    print("   python quality_control_generator.py              # 默认交互式")
    print("   python quality_control_generator.py --interactive")
    print("   python quality_control_generator.py -i")
    print()
    print("✅ 所有现有功能保持不变")
    print("✅ 新功能作为增强，不影响现有使用方式")
    print("✅ 三层架构、代码清理等功能完全兼容")

def demo_user_experience():
    """演示用户体验优化"""
    print("\n" + "=" * 80)
    print("✨ 演示：用户体验优化")
    print("=" * 80)
    
    print("交互式体验特色：")
    print()
    print("1. 🎯 智能输入识别")
    print("   • 支持编号、完整名称、简化名称")
    print("   • 大小写不敏感")
    print("   • 自动文件名标准化")
    print()
    print("2. 🛡️ 完善的错误处理")
    print("   • 输入验证和提示")
    print("   • 清晰的错误信息")
    print("   • 重新输入机会")
    print()
    print("3. 📋 详细的配置摘要")
    print("   • 选择确认显示")
    print("   • 完整配置预览")
    print("   • 最终确认机制")
    print()
    print("4. 🚪 灵活的退出机制")
    print("   • 任何阶段都可以退出")
    print("   • Ctrl+C 中断支持")
    print("   • 友好的退出提示")

def demo_advanced_features():
    """演示高级功能"""
    print("\n" + "=" * 80)
    print("🚀 演示：高级功能特性")
    print("=" * 80)
    
    print("模型配置灵活性：")
    print("  • 7种不同的LLM模型可选")
    print("  • 规则质控和内涵质控可独立配置")
    print("  • 预设配置方案快速选择")
    print("  • 自定义配置完全控制")
    print()
    print("文档类型自动发现：")
    print("  • 自动扫描JSON配置文件")
    print("  • 动态生成选择菜单")
    print("  • 支持新文档类型无需修改代码")
    print()
    print("集成现有功能：")
    print("  • 无缝集成三层架构设计")
    print("  • 自动调用代码清理功能")
    print("  • 保持所有现有质控逻辑")
    print("  • 完整的测试文件组织")

def main():
    """主演示函数"""
    print("🎉 质控代码生成器交互式功能演示")
    print("=" * 80)
    print("展示新增的交互式文档选择和模型配置功能")
    print("=" * 80)
    
    # 运行各项演示
    demo_document_selection()
    demo_model_selection()
    demo_input_validation()
    demo_chinese_support()
    demo_compatibility()
    demo_user_experience()
    demo_advanced_features()
    
    print("\n" + "=" * 80)
    print("🎯 交互式功能演示总结")
    print("=" * 80)
    print("✅ 文档类型交互式选择 - 支持多种输入方式")
    print("✅ 模型配置交互式选择 - 灵活的配置方案")
    print("✅ 智能输入验证 - 用户友好的错误处理")
    print("✅ 中文界面支持 - 本土化用户体验")
    print("✅ 完全向后兼容 - 不影响现有功能")
    print("✅ 用户体验优化 - 直观易用的交互设计")
    print("✅ 高级功能集成 - 无缝融入现有架构")
    print("=" * 80)
    print()
    print("🚀 立即体验交互式模式：")
    print("   cd dev_v1/Quality_Control")
    print("   python quality_control_generator.py")
    print()
    print("📖 查看帮助信息：")
    print("   python quality_control_generator.py --help")
    print()
    print("🧪 运行功能测试：")
    print("   python tests/test_interactive_features.py")

if __name__ == "__main__":
    main()
