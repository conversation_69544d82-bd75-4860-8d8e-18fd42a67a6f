# 规则ID: rule_ad11ea7b
# 描述: 首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断

import re

def check_rule(medical_record):
    """
    检查首次病程记录是否缺少鉴别诊断
    当诊断不包含待查类时进行判断
    """
    try:
        # 提取首次病程记录和诊断信息
        first_course = medical_record.get('first_course', '')
        diagnosis = medical_record.get('diagnosis', '')
        
        # 检查是否存在鉴别诊断相关内容
        has_diff_diag = any(
            keyword in first_course 
            for keyword in ["鉴别诊断", "鉴别诊断：", "鉴别诊断：", "鉴别诊断如下"]
        )
        
        # 检查诊断是否为待查类型
        is_pending = any(
            keyword in diagnosis 
            for keyword in ["待查", "?", "待进一步检查", "原因不明"]
        )
        
        # 规则逻辑：首次病程缺鉴别诊断且诊断不是待查类
        if not has_diff_diag and not is_pending:
            return True
        return False
            
    except Exception as e:
        # 出现异常时默认不扣分
        return False

if __name__ == '__main__':
    # 测试用例1: 正常情况(有鉴别诊断)
    test1 = {
        'first_course': '患者入院后完善检查，鉴别诊断考虑：1. 肺炎 2. 肺结核',
        'diagnosis': '肺炎'
    }
    assert check_rule(test1) == False
    
    # 测试用例2: 诊断为待查类
    test2 = {
        'first_course': '患者入院后完善检查',
        'diagnosis': '发热待查'
    }
    assert check_rule(test2) == False
    
    # 测试用例3: 缺鉴别诊断且诊断非待查类
    test3 = {
        'first_course': '患者入院后完善血常规检查',
        'diagnosis': '肺炎'
    }
    assert check_rule(test3) == True
    
    # 测试用例4: 缺鉴别诊断但诊断含待查关键词
    test4 = {
        'first_course': '患者入院后完善检查',
        'diagnosis': '肺炎待查'
    }
    assert check_rule(test4) == False
    
    # 测试用例5: 异常数据处理
    test5 = {
        'first_course': None,
        'diagnosis': '肺炎'
    }
    assert check_rule(test5) == False
    
    print("所有测试用例通过")