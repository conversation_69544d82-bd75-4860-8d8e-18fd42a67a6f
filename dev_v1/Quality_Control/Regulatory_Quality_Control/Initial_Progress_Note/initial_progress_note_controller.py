# -*- coding: utf-8 -*-
"""
Initial Progress Note 规则质控子控制器
负责协调该文档类型下的所有规则质控检查
"""
import sys
from pathlib import Path

# 导入所有规则检查函数
from .rule_a20a29aa import check_rule
from .rule_a20a29aa import check_rule as rule_a20a29aa_check
from .rule_4e774c85 import check_rule
from .rule_4e774c85 import check_rule as rule_4e774c85_check
from .rule_427f6155 import check_rule
from .rule_427f6155 import check_rule as rule_427f6155_check
from .rule_2415e6ae import check_rule
from .rule_2415e6ae import check_rule as rule_2415e6ae_check
from .rule_3d8e749b import check_rule
from .rule_3d8e749b import check_rule as rule_3d8e749b_check
from .rule_f06c1f38 import check_rule
from .rule_f06c1f38 import check_rule as rule_f06c1f38_check
from .rule_deda3efa import check_rule
from .rule_deda3efa import check_rule as rule_deda3efa_check
from .rule_7699d643 import check_rule
from .rule_7699d643 import check_rule as rule_7699d643_check
from .rule_97ee5e07 import check_rule
from .rule_97ee5e07 import check_rule as rule_97ee5e07_check
from .rule_3074de0d import check_rule
from .rule_3074de0d import check_rule as rule_3074de0d_check
from .rule_ad11ea7b import check_rule
from .rule_ad11ea7b import check_rule as rule_ad11ea7b_check
from .rule_ff5a0e2b import check_rule
from .rule_ff5a0e2b import check_rule as rule_ff5a0e2b_check

class InitialProgressNoteRegulatoryController:
    """
    Initial Progress Note 规则质控控制器
    """

    def __init__(self):
        self.document_type = "Initial Progress Note"
        self.total_rules = 12

    def run_regulatory_quality_control(self, medical_record):
        """
        运行所有规则质控检查

        Args:
            medical_record (dict): 病历数据

        Returns:
            dict: 规则质控结果
        """
        results = {}

        print(f"开始执行 {self.document_type} 规则质控，共 {self.total_rules} 条规则...")

        # 执行规则: 首次病程未在患者入院后8小时内完成
        try:
            result = rule_a20a29aa_check(medical_record)
            results['首次病程未在患者入院后8小时内完成'] = {
                'rule_id': 'rule_a20a29aa',
                'rule_type': '时效性',
                'classification': '手术科室',
                'deduction_points': 60.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_a20a29aa 执行失败: {e}')
            results['首次病程未在患者入院后8小时内完成'] = {
                'rule_id': 'rule_a20a29aa',
                'rule_type': '时效性',
                'classification': '手术科室',
                'deduction_points': 60.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺病例特点
        try:
            result = rule_4e774c85_check(medical_record)
            results['首次病程缺病例特点'] = {
                'rule_id': 'rule_4e774c85',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_4e774c85 执行失败: {e}')
            results['首次病程缺病例特点'] = {
                'rule_id': 'rule_4e774c85',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺初步诊断
        try:
            result = rule_427f6155_check(medical_record)
            results['首次病程缺初步诊断'] = {
                'rule_id': 'rule_427f6155',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_427f6155 执行失败: {e}')
            results['首次病程缺初步诊断'] = {
                'rule_id': 'rule_427f6155',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺诊断依据
        try:
            result = rule_2415e6ae_check(medical_record)
            results['首次病程缺诊断依据'] = {
                'rule_id': 'rule_2415e6ae',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_2415e6ae 执行失败: {e}')
            results['首次病程缺诊断依据'] = {
                'rule_id': 'rule_2415e6ae',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断
        try:
            result = rule_3d8e749b_check(medical_record)
            results['首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断'] = {
                'rule_id': 'rule_3d8e749b',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_3d8e749b 执行失败: {e}')
            results['首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断'] = {
                'rule_id': 'rule_3d8e749b',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺诊疗计划
        try:
            result = rule_f06c1f38_check(medical_record)
            results['首次病程缺诊疗计划'] = {
                'rule_id': 'rule_f06c1f38',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_f06c1f38 执行失败: {e}')
            results['首次病程缺诊疗计划'] = {
                'rule_id': 'rule_f06c1f38',
                'rule_type': '段落完整性',
                'classification': '手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程未在患者入院后8小时内完成
        try:
            result = rule_deda3efa_check(medical_record)
            results['首次病程未在患者入院后8小时内完成'] = {
                'rule_id': 'rule_deda3efa',
                'rule_type': '时效性',
                'classification': '非手术科室',
                'deduction_points': 60.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_deda3efa 执行失败: {e}')
            results['首次病程未在患者入院后8小时内完成'] = {
                'rule_id': 'rule_deda3efa',
                'rule_type': '时效性',
                'classification': '非手术科室',
                'deduction_points': 60.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺病例特点
        try:
            result = rule_7699d643_check(medical_record)
            results['首次病程缺病例特点'] = {
                'rule_id': 'rule_7699d643',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_7699d643 执行失败: {e}')
            results['首次病程缺病例特点'] = {
                'rule_id': 'rule_7699d643',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺初步诊断
        try:
            result = rule_97ee5e07_check(medical_record)
            results['首次病程缺初步诊断'] = {
                'rule_id': 'rule_97ee5e07',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_97ee5e07 执行失败: {e}')
            results['首次病程缺初步诊断'] = {
                'rule_id': 'rule_97ee5e07',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺诊断依据
        try:
            result = rule_3074de0d_check(medical_record)
            results['首次病程缺诊断依据'] = {
                'rule_id': 'rule_3074de0d',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_3074de0d 执行失败: {e}')
            results['首次病程缺诊断依据'] = {
                'rule_id': 'rule_3074de0d',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断
        try:
            result = rule_ad11ea7b_check(medical_record)
            results['首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断'] = {
                'rule_id': 'rule_ad11ea7b',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_ad11ea7b 执行失败: {e}')
            results['首次病程缺鉴别诊断，诊断不存在待查类的病人，可不判断'] = {
                'rule_id': 'rule_ad11ea7b',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }

        # 执行规则: 首次病程缺诊疗计划
        try:
            result = rule_ff5a0e2b_check(medical_record)
            results['首次病程缺诊疗计划'] = {
                'rule_id': 'rule_ff5a0e2b',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': result,
                'type': 'regulatory'
            }
        except Exception as e:
            print(f'规则 rule_ff5a0e2b 执行失败: {e}')
            results['首次病程缺诊疗计划'] = {
                'rule_id': 'rule_ff5a0e2b',
                'rule_type': '段落完整性',
                'classification': '非手术科室',
                'deduction_points': 6.0,
                'has_problem': True,  # 执行失败视为有问题
                'type': 'regulatory',
                'error': str(e)
            }


        print(f"{self.document_type} 规则质控执行完成，共检查 {len(results)} 条规则")
        return results

    def get_summary(self, results):
        """
        获取质控结果摘要

        Args:
            results (dict): 质控结果

        Returns:
            dict: 摘要信息
        """
        total_rules = len(results)
        problem_rules = sum(1 for r in results.values() if r.get('has_problem', False))
        total_deduction = sum(r.get('deduction_points', 0) for r in results.values() if r.get('has_problem', False))

        return {
            'document_type': self.document_type,
            'total_rules': total_rules,
            'problem_rules': problem_rules,
            'total_deduction_points': total_deduction,
            'compliance_rate': (total_rules - problem_rules) / total_rules * 100 if total_rules > 0 else 0
        }

# 兼容性函数，供主控制器调用
def run_regulatory_quality_control(medical_record):
    """兼容性函数"""
    controller = InitialProgressNoteRegulatoryController()
    return controller.run_regulatory_quality_control(medical_record)

if __name__ == "__main__":
    # 测试示例
    controller = InitialProgressNoteRegulatoryController()

    test_record = {
        "content": "测试Initial Progress Note内容",
        "patient_info": {},
        "diagnosis": {},
        "treatment": {}
    }

    results = controller.run_regulatory_quality_control(test_record)
    summary = controller.get_summary(results)

    print("\n规则质控结果摘要:")
    print(f"文档类型: {summary['document_type']}")
    print(f"总规则数: {summary['total_rules']}")
    print(f"问题规则数: {summary['problem_rules']}")
    print(f"总扣分: {summary['total_deduction_points']}")
    print(f"合规率: {summary['compliance_rate']:.2f}%")
