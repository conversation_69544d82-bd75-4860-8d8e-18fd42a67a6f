# rule_f071c274: 缺出院记录，或未在出院后24小时内完成
# 规则类型：时效性 (Timeliness)
# 扣分：60.0分

import datetime
from typing import Dict, Any

def check_rule(medical_record: Dict[str, Any]) -> bool:
    """
    检查病历是否缺出院记录，或未在出院后24小时内完成
    
    参数:
        medical_record: 病历数据字典
        
    返回:
        bool: True表示有问题（缺出院记录或未在24小时内完成），False表示没有问题
    """
    try:
        # 检查是否有出院记录
        discharge_records = medical_record.get('discharge_records', [])
        
        if not discharge_records:
            # 没有出院记录，违反规则
            return True
            
        # 检查每条出院记录是否在出院后24小时内完成
        for record in discharge_records:
            discharge_time = record.get('discharge_time')
            record_completion_time = record.get('record_completion_time')
            
            if not discharge_time or not record_completion_time:
                # 缺少必要时间信息，违反规则
                return True
                
            # 将时间字符串转换为datetime对象
            discharge_dt = datetime.datetime.fromisoformat(discharge_time)
            completion_dt = datetime.datetime.fromisoformat(record_completion_time)
            
            # 计算时间差
            time_diff = completion_dt - discharge_dt
            
            # 检查是否超过24小时
            if time_diff.total_seconds() > 24 * 3600:
                # 超过24小时完成，违反规则
                return True
                
        # 所有出院记录都符合要求
        return False
        
    except Exception as e:
        # 处理任何可能的异常
        print(f"检查出院记录时效性时发生错误: {str(e)}")
        # 发生异常时，默认返回True表示有问题
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 没有出院记录
    test_record1 = {
        "patient_id": "12345",
        "discharge_records": []
    }
    print("测试1 (无出院记录):", check_rule(test_record1))  # 应该返回True
    
    # 测试用例2: 有出院记录，但在24小时内完成
    test_record2 = {
        "patient_id": "12345",
        "discharge_records": [
            {
                "discharge_time": "2023-01-01T10:00:00",
                "record_completion_time": "2023-01-01T12:30:00"
            }
        ]
    }
    print("测试2 (24小时内完成):", check_rule(test_record2))  # 应该返回False
    
    # 测试用例3: 有出院记录，但超过24小时完成
    test_record3 = {
        "patient_id": "12345",
        "discharge_records": [
            {
                "discharge_time": "2023-01-01T10:00:00",
                "record_completion_time": "2023-01-02T12:30:00"
            }
        ]
    }
    print("测试3 (超过24小时完成):", check_rule(test_record3))  # 应该返回True
    
    # 测试用例4: 缺少出院时间
    test_record4 = {
        "patient_id": "12345",
        "discharge_records": [
            {
                "record_completion_time": "2023-01-01T12:30:00"
            }
        ]
    }
    print("测试4 (缺少出院时间):", check_rule(test_record4))  # 应该返回True
    
    # 测试用例5: 缺少记录完成时间
    test_record5 = {
        "patient_id": "12345",
        "discharge_records": [
            {
                "discharge_time": "2023-01-01T10:00:00"
            }
        ]
    }
    print("测试5 (缺少记录完成时间):", check_rule(test_record5))  # 应该返回True
    
    # 测试用例6: 多条出院记录，其中一条超过24小时
    test_record6 = {
        "patient_id": "12345",
        "discharge_records": [
            {
                "discharge_time": "2023-01-01T10:00:00",
                "record_completion_time": "2023-01-01T12:30:00"
            },
            {
                "discharge_time": "2023-01-02T10:00:00",
                "record_completion_time": "2023-01-03T12:30:00"
            }
        ]
    }
    print("测试6 (多条记录，其中一条超时):", check_rule(test_record6))  # 应该返回True