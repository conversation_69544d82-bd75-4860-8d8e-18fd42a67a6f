# rule_b24ccb01 - 段落完整性 (Section Completeness): 缺诊疗经过
# 扣分: 6.0分

def check_rule(medical_record):
    """
    检查病历中是否缺诊疗经过部分
    
    参数:
        medical_record (dict): 包含病历数据的字典
        
    返回:
        bool: True表示缺诊疗经过(有问题)，False表示不缺(没有问题)
    """
    try:
        # 检查medical_record是否为字典类型
        if not isinstance(medical_record, dict):
            return True  # 如果输入不是字典，视为有问题
            
        # 检查是否存在诊疗经过部分
        # 假设诊疗经过在medical_record中的键为"诊疗经过"或"diagnostic_process"
        if "诊疗经过" not in medical_record and "diagnostic_process" not in medical_record:
            return True
            
        # 检查诊疗经过部分是否为空
        diagnostic_process = medical_record.get("诊疗经过") or medical_record.get("diagnostic_process", "")
        if not diagnostic_process or not str(diagnostic_process).strip():
            return True
            
        return False
        
    except Exception as e:
        # 记录错误日志（在实际应用中）
        # print(f"检查诊疗经过时发生错误: {str(e)}")
        # 发生异常时，默认返回True表示有问题
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正常病历，有诊疗经过
    normal_record = {
        "患者基本信息": {"姓名": "张三", "性别": "男"},
        "主诉": "头痛3天",
        "诊疗经过": "患者3天前无明显诱因出现头痛，呈持续性，位于额部，程度中等，无恶心呕吐。自行服用止痛药后症状稍缓解。"
    }
    print("测试1 - 有诊疗经过:", check_rule(normal_record))  # 应该返回False
    
    # 测试用例2: 缺少诊疗经过
    missing_record = {
        "患者基本信息": {"姓名": "李四", "性别": "女"},
        "主诉": "腹痛2天"
        # 没有诊疗经过部分
    }
    print("测试2 - 缺诊疗经过:", check_rule(missing_record))  # 应该返回True
    
    # 测试用例3: 诊疗经过为空
    empty_record = {
        "患者基本信息": {"姓名": "王五", "性别": "男"},
        "主诉": "发热1天",
        "诊疗经过": ""  # 诊疗经过为空
    }
    print("测试3 - 诊疗经过为空:", check_rule(empty_record))  # 应该返回True
    
    # 测试用例4: 诊疗经过只有空白字符
    whitespace_record = {
        "患者基本信息": {"姓名": "赵六", "性别": "女"},
        "主诉": "咳嗽5天",
        "诊疗经过": "   "  # 只有空白字符
    }
    print("测试4 - 诊疗经过只有空白字符:", check_rule(whitespace_record))  # 应该返回True
    
    # 测试用例5: 使用英文键名
    english_key_record = {
        "patient_info": {"name": "钱七", "gender": "男"},
        "chief_complaint": "胸痛1天",
        "diagnostic_process": "患者1天前出现胸痛，位于胸部左侧，深呼吸时加重。"
    }
    print("测试5 - 使用英文键名:", check_rule(english_key_record))  # 应该返回False
    
    # 测试用例6: 输入不是字典
    not_dict_input = "这不是一个字典"
    print("测试6 - 输入不是字典:", check_rule(not_dict_input))  # 应该返回True