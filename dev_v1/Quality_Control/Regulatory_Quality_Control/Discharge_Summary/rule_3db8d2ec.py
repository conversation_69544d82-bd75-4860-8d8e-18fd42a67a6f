# Rule ID: rule_3db8d2ec
# Description: 段落完整性 - 缺出院诊断 (扣6.0分)

def check_rule(medical_record):
    """检查病历是否缺少出院诊断字段"""
    try:
        # 如果出院诊断字段不存在或为空字符串则判定违规
        if not medical_record.get('discharge_diagnosis', ''):
            return True
        return False
    except Exception as e:
        # 捕获所有异常并返回False
        return False

if __name__ == "__main__":
    # 测试用例1: 缺出院诊断字段
    test1 = {'admission_diagnosis': 'Pneumonia'}
    print(check_rule(test1))  # 预期输出: True
    
    # 测试用例2: 有出院诊断字段
    test2 = {'discharge_diagnosis': 'Recovered from Pneumonia'}
    print(check_rule(test2))  # 预期输出: False
    
    # 测试用例3: 出院诊断字段为空字符串
    test3 = {'discharge_diagnosis': ''}
    print(check_rule(test3))  # 预期输出: True
    
    # 测试用例4: medical_record为None
    test4 = None
    print(check_rule(test4))  # 预期输出: False