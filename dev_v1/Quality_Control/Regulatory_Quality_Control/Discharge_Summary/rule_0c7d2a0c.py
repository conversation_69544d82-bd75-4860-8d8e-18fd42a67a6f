# 规则ID: rule_0c7d2a0c
# 描述: 检查出院前一天或当天是否有上级医生同意出院记录

from datetime import datetime, timedelta

def check_rule(medical_record):
    """
    检查病历中是否缺少出院前一天或当天的上级医生同意记录
    返回True表示存在问题，False表示符合要求
    """
    try:
        # 获取出院日期
        discharge_date_str = medical_record.get('discharge_date')
        if not discharge_date_str:
            return True  # 缺失关键信息直接判定为问题
            
        discharge_date = datetime.strptime(discharge_date_str, '%Y-%m-%d').date()
        
        # 计算前一天日期
        previous_day = discharge_date - timedelta(days=1)
        
        # 检查审批记录
        approvals = medical_record.get('approvals', [])
        
        # 检查是否有上级医生同意记录在目标日期
        has_approval = any(
            (approval.get('date') == discharge_date or 
             approval.get('date') == previous_day) and
            approval.get('type') == 'physician_approval'
            for approval in approvals
        )
        
        return not has_approval  # 如果没有找到匹配记录则返回True(问题)
        
    except Exception as e:
        # 任何异常都视为规则检查失败
        return True

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 正常情况(有当天记录)
    test1 = {
        'discharge_date': '2023-05-10',
        'approvals': [{
            'date': '2023-05-10',
            'type': 'physician_approval'
        }]
    }
    print("Test1:", check_rule(test1))  # 应返回False
    
    # 测试用例2: 正常情况(有前一天记录)
    test2 = {
        'discharge_date': '2023-05-10',
        'approvals': [{
            'date': '2023-05-09',
            'type': 'physician_approval'
        }]
    }
    print("Test2:", check_rule(test2))  # 应返回False
    
    # 测试用例3: 缺失记录
    test3 = {
        'discharge_date': '2023-05-10',
        'approvals': [{
            'date': '2023-05-08',
            'type': 'nurse_approval'
        }]
    }
    print("Test3:", check_rule(test3))  # 应返回True
    
    # 测试用例4: 日期格式错误
    test4 = {
        'discharge_date': 'invalid-date',
        'approvals': []
    }
    print("Test4:", check_rule(test4))  # 应返回True