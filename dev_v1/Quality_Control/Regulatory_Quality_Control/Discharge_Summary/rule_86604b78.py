# rule_86604b78 - 缺入院诊断
# 检查病历中是否缺少入院诊断信息

import logging
from typing import Dict, Any

def check_rule(medical_record: Dict[str, Any]) -> bool:
    """
    检查病历中是否缺少入院诊断信息
    
    参数:
        medical_record: 病历数据字典
        
    返回:
        bool: True表示缺少入院诊断(有问题)，False表示有入院诊断(没有问题)
    """
    try:
        # 检查病历数据是否为字典类型
        if not isinstance(medical_record, dict):
            logging.warning("病历数据不是字典类型")
            return True
            
        # 可能的入院诊断字段名列表
        possible_diagnosis_fields = [
            "入院诊断", 
            "admission_diagnosis", 
            "入院诊断信息",
            "admission_diagnosis_info",
            "diagnosis"
        ]
        
        # 检查是否存在入院诊断信息
        for field in possible_diagnosis_fields:
            if field in medical_record:
                diagnosis_value = medical_record[field]
                # 检查诊断值是否为空或无效
                if diagnosis_value and str(diagnosis_value).strip():
                    return False  # 找到有效的入院诊断信息
        
        # 如果没有找到任何入院诊断字段或字段值为空
        return True
        
    except Exception as e:
        logging.error(f"检查入院诊断时发生错误: {str(e)}")
        return True  # 发生错误时默认认为有问题

# 测试代码
if __name__ == "__main__":
    # 测试用例1: 有入院诊断
    record_with_diagnosis = {
        "入院诊断": "肺炎",
        "患者信息": "张三"
    }
    print("测试1 - 有入院诊断:", check_rule(record_with_diagnosis))  # 应该返回False
    
    # 测试用例2: 无入院诊断
    record_without_diagnosis = {
        "患者信息": "李四",
        "主诉": "咳嗽"
    }
    print("测试2 - 无入院诊断:", check_rule(record_without_diagnosis))  # 应该返回True
    
    # 测试用例3: 入院诊断为空
    record_with_empty_diagnosis = {
        "入院诊断": "",
        "患者信息": "王五"
    }
    print("测试3 - 入院诊断为空:", check_rule(record_with_empty_diagnosis))  # 应该返回True
    
    # 测试用例4: 英文字段名有入院诊断
    record_with_english_diagnosis = {
        "admission_diagnosis": "高血压",
        "patient_info": "赵六"
    }
    print("测试4 - 英文字段名有入院诊断:", check_rule(record_with_english_diagnosis))  # 应该返回False
    
    # 测试用例5: 非字典类型输入
    print("测试5 - 非字典类型输入:", check_rule("invalid_input"))  # 应该返回True